// Test utilities for manual testing

export const testCredentials = {
  validUser: {
    email: '<EMAIL>',
    password: 'testPassword123',
  },
  invalidUser: {
    email: '<EMAIL>',
    password: 'wrongPassword',
  },
  weakPassword: {
    email: '<EMAIL>',
    password: '123', // Too short
  },
  invalidEmail: {
    email: 'invalid-email',
    password: 'testPassword123',
  },
}

export const testFormData = {
  profile: {
    username: 'testuser',
    full_name: 'Test User',
    date_of_birth: '1990-01-01',
    gender: 'male',
  },
}

// Mock API responses for testing
export const mockResponses = {
  registerSuccess: {
    success: true,
    message: 'Registration successful! Please login.',
  },
  registerError: {
    success: false,
    error: {
      message: 'Email already exists',
    },
  },
  loginSuccess: {
    success: true,
    data: {
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        user_type: 'user',
        token_balance: 100,
      },
      token: 'mock-jwt-token',
    },
  },
  loginError: {
    success: false,
    error: {
      message: 'Invalid credentials',
    },
  },
}

// Test scenarios
export const testScenarios = [
  {
    name: 'Valid Registration',
    description: 'Test registration with valid email and password',
    credentials: testCredentials.validUser,
    expectedResult: 'success',
  },
  {
    name: 'Invalid Email Registration',
    description: 'Test registration with invalid email format',
    credentials: testCredentials.invalidEmail,
    expectedResult: 'validation error',
  },
  {
    name: 'Weak Password Registration',
    description: 'Test registration with weak password',
    credentials: testCredentials.weakPassword,
    expectedResult: 'validation error',
  },
  {
    name: 'Valid Login',
    description: 'Test login with valid credentials',
    credentials: testCredentials.validUser,
    expectedResult: 'success',
  },
  {
    name: 'Invalid Login',
    description: 'Test login with invalid credentials',
    credentials: testCredentials.invalidUser,
    expectedResult: 'authentication error',
  },
]

// Helper functions for testing
export const testHelpers = {
  // Simulate form input
  fillForm: (formData) => {
    console.log('Filling form with:', formData)
    // This would be used with testing libraries like Testing Library
  },
  
  // Simulate button click
  clickButton: (buttonText) => {
    console.log('Clicking button:', buttonText)
  },
  
  // Check if element exists
  elementExists: (selector) => {
    return document.querySelector(selector) !== null
  },
  
  // Wait for element
  waitForElement: async (selector, timeout = 5000) => {
    return new Promise((resolve, reject) => {
      const startTime = Date.now()
      const checkElement = () => {
        const element = document.querySelector(selector)
        if (element) {
          resolve(element)
        } else if (Date.now() - startTime > timeout) {
          reject(new Error(`Element ${selector} not found within ${timeout}ms`))
        } else {
          setTimeout(checkElement, 100)
        }
      }
      checkElement()
    })
  },
}

// Manual testing instructions
export const testingInstructions = `
MANUAL TESTING INSTRUCTIONS FOR ATMA FRONTEND

1. REGISTRATION TESTING:
   - Navigate to /register
   - Test with valid email and password (min 8 chars, letter + number)
   - Test with invalid email format
   - Test with weak password
   - Verify validation messages appear
   - Verify success message on successful registration

2. LOGIN TESTING:
   - Navigate to /login
   - Test with valid credentials
   - Test with invalid credentials
   - Verify error messages appear
   - Verify redirect to dashboard on success

3. DASHBOARD TESTING:
   - Verify user information displays correctly
   - Verify token balance shows
   - Test navigation to profile page
   - Test logout functionality

4. PROFILE TESTING:
   - Navigate to /profile
   - Test form validation
   - Test profile update (mock)
   - Test navigation back to dashboard

5. NAVIGATION TESTING:
   - Test protected routes (should redirect to login if not authenticated)
   - Test 404 page with invalid URLs
   - Test all navigation links

6. RESPONSIVE TESTING:
   - Test on different screen sizes
   - Verify mobile responsiveness
   - Test touch interactions

7. ERROR HANDLING:
   - Test with network disconnected
   - Test with invalid API responses
   - Verify error messages are user-friendly

8. ACCESSIBILITY TESTING:
   - Test keyboard navigation
   - Test screen reader compatibility
   - Verify proper focus management

Test Credentials:
- Email: <EMAIL>
- Password: testPassword123

Note: Since backend is not running, API calls will fail.
This is expected behavior for frontend-only testing.
`

console.log(testingInstructions)
